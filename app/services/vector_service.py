from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings


def load_vector_db_for_company(company_id: str):
    """Load vector databases for a specific company."""
    base_path = f"./vector_dbs/{company_id}"
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")

    coarse_vdb = FAISS.load_local(
        f"{base_path}/coarse_index",
        embeddings,
        allow_dangerous_deserialization=True
    )
    fine_vdb = FAISS.load_local(f"{base_path}/fine_index", embeddings, allow_dangerous_deserialization=True)
    return coarse_vdb, fine_vdb


def split_docs(docs, chunk_size: int = 800, overlap: int = 50):
    """Split documents into chunks."""
    splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=overlap)
    return splitter.split_documents(docs)


def create_vector_db(chunks):
    """Create vector database from document chunks."""
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")
    return FAISS.from_documents(chunks, embeddings)
