import logging
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings

logger = logging.getLogger(__name__)

def load_vector_db_for_company(company_id: str):
    logger.info("Loading vector DBs for company %s…", company_id)

    base_path = f"./vector_dbs/{company_id}"
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")

    coarse_vdb = FAISS.load_local(
        f"{base_path}/coarse_index",
        embeddings,
        allow_dangerous_deserialization=True
    )
    fine_vdb = FAISS.load_local(f"{base_path}/fine_index", embeddings, allow_dangerous_deserialization=True)
    return coarse_vdb, fine_vdb
