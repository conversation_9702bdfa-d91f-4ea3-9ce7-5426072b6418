import os
import logging
import fitz  # PyMuPDF
import boto3
from dotenv import load_dotenv
from langchain.schema import Document
from typing import List
from io import BytesIO
from langchain.document_loaders import PyMuPDFLoader
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter

logger = logging.getLogger(__name__)

def ingest_documents_for_company(company_id: str, bucket: str, s3_prefix: str):
    docs = load_pdfs_from_s3(bucket, s3_prefix)

    coarse_vdb, fine_vdb = create_hierarchical_vector_db(docs)

    base_path = f"./vector_dbs/{company_id}"
    os.makedirs(base_path, exist_ok=True)

    coarse_vdb.save_local(f"{base_path}/coarse_index")
    fine_vdb.save_local(f"{base_path}/fine_index")
    logger.info("✅ Vector stores saved for company %s", company_id)

def load_pdfs(folder: str):
    logger.info("📂 Scanning folder: %s", folder)
    docs = []

    # --- Load PDFs ---
    for file in os.listdir(folder):
        if file.lower().endswith(".pdf"):
            with fitz.open(os.path.join(folder, file)) as pdf:
                text = "".join(page.get_text() for page in pdf)
            is_annual_report = "annual" in file.lower()
            metadata = {
                "source": file,
                "always_include": is_annual_report
            }
            doc = Document(page_content=text, metadata=metadata)
            docs.append(doc)
            logger.info("📄 Loaded: %s", file)
            logger.debug("🔎 Metadata: %s", doc.metadata)

    return docs

def create_hierarchical_vector_db(docs):
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")
    
    coarse_docs = []
    fine_docs = []

    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)
    
    for doc in docs:
        # If it's a website doc, don't chunk — add as one document
        if doc.metadata.get("source", "").startswith("http"):
          chunks = splitter.split_documents([doc])
          for i, chunk in enumerate(chunks):
              chunk.metadata.update({
                  "doc_source": doc.metadata["source"],
                  "always_include": True,
                  "chunk_id": i,
                  "level": "fine"
              })
              fine_docs.append(chunk)

          # Optionally keep one for coarse index:
          rep_chunk = chunks[0]
          rep_chunk.metadata.update({
            "doc_source": doc.metadata["source"],
            "always_include": True,
            "level": "coarse"
          })
          coarse_docs.append(rep_chunk)
        else:
            # For PDFs or other docs, do chunking as before
            chunks = splitter.split_documents([doc])
            print("DOC", doc.metadata["source"])
            rep_chunk = chunks[0]
            rep_chunk.metadata["doc_source"] = doc.metadata["source"]
            rep_chunk.metadata["always_include"] = doc.metadata.get("always_include", False)
            rep_chunk.metadata["level"] = "coarse"
            coarse_docs.append(rep_chunk)

            for i, chunk in enumerate(chunks):
                chunk.metadata["doc_source"] = doc.metadata["source"]
                chunk.metadata["always_include"] = doc.metadata.get("always_include", False)
                chunk.metadata["chunk_id"] = i
                chunk.metadata["level"] = "fine"
                fine_docs.append(chunk)

    coarse_vdb = FAISS.from_documents(coarse_docs, embeddings)
    fine_vdb = FAISS.from_documents(fine_docs, embeddings)
    return coarse_vdb, fine_vdb



def load_pdfs_from_s3(bucket: str, prefix: str) -> List[Document]:
    logger.info("📂 Scanning S3 folder: s3://%s/%s", bucket, prefix)
    docs = []

    # List all PDFs under prefix
    load_dotenv()
    s3 = boto3.client('s3')
    paginator = s3.get_paginator("list_objects_v2")
    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        for obj in page.get("Contents", []):
            key = obj["Key"]
            if not key.lower().endswith(".pdf"):
                continue

            logger.info("⬇️ Downloading: %s", key)
            response = s3.get_object(Bucket=bucket, Key=key)
            pdf_stream = BytesIO(response["Body"].read())

            with fitz.open(stream=pdf_stream, filetype="pdf") as pdf:
                text = "".join(page.get_text() for page in pdf)

            filename = key.split("/")[-1]
            is_annual_report = "annual" in filename.lower()
            metadata = {
                "source": filename,
                "always_include": is_annual_report
            }

            doc = Document(page_content=text, metadata=metadata)
            docs.append(doc)
            logger.info("📄 Loaded: %s", filename)
            logger.debug("🔎 Metadata: %s", metadata)

    return docs





