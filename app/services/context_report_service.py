import os
import json
import fitz  # PyMuPDF
from dotenv import load_dotenv
from anthropic import Anthropic
import tiktoken
import time
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from app.services.prompt import get_context_report_data, extract_prompt_list
from langchain_aws import ChatBedrock
from langchain.schema import HumanMessage, SystemMessage
import boto3
from langchain.schema.messages import BaseMessage
from typing import List, Optional
from langchain.schema import ChatResult,AIMessage
import re

async def prefillContextReport(company_id: str, data: any):
    print(f"📦 Loading vector DBs for company {company_id}…")
    coarse_vdb, fine_vdb = load_vector_db_for_company(company_id)

    print("🤖 Running hierarchical RAG for report generation…")
    start_time = time.time()
    overview = await retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data)
    end_time = time.time()

    print(f"\n⏱️ Duration: {end_time - start_time:.2f}s")
    return overview

def count_tokens(text: str, model_name: str = "claude-sonnet-4-20250514") -> int:
    if "gpt" in model_name:
        enc = tiktoken.encoding_for_model(model_name)
        return len(enc.encode(text))
    else:
        return len(text) // 4

def split_docs(docs, chunk_size: int = 800, overlap: int = 50):
    splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=overlap)
    return splitter.split_documents(docs)

def create_vector_db(chunks):
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")
    return FAISS.from_documents(chunks, embeddings)

def json_to_text(data):
    lines = []
    for item in data:
        lines.append(f"{item['id']}")
        lines.append(f"  {item['content']}\n")
    return "\n".join(lines)


async def retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data: any, k_docs: int = 3):
    queries = extract_prompt_list(data)
    prompt = get_context_report_data(data)

    top_docs = set()
    for q in queries:
        coarse_hits = coarse_vdb.similarity_search(q, k=k_docs)
        top_docs.update(hit.metadata["doc_source"] for hit in coarse_hits)

    for doc in coarse_vdb.docstore._dict.values():
        if doc.metadata.get("always_include", False):
            top_docs.add(doc.metadata["doc_source"])

    print(f"📘 Top doc sources selected: {top_docs}")

    all_chunks = []
    for q in queries:
        fine_hits = fine_vdb.similarity_search_with_score(q, k=20)
        for doc, score in fine_hits:
            if doc.metadata["doc_source"] in top_docs:
                all_chunks.append(doc)

    seen = set()
    unique_chunks = []
    for doc in all_chunks:
        if doc.page_content not in seen:
            unique_chunks.append(doc)
            seen.add(doc.page_content)

    context = "\n\n".join(
        f"[Source: {doc.metadata['doc_source']}]\n{doc.page_content}"
        for doc in unique_chunks)
    total_prompt = prompt + "\n\nContext:\n" + context
    print(f"🧾 Input tokens: {count_tokens(total_prompt)}")
    response_text = call_claude_3(total_prompt)
    return response_text

def load_vector_db_for_company(company_id: str):
    base_path = f"./vector_dbs/{company_id}"
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")

    coarse_vdb = coarse_vdb = FAISS.load_local(
    f"{base_path}/coarse_index",
    embeddings,
    allow_dangerous_deserialization=True
)
    fine_vdb = FAISS.load_local(f"{base_path}/fine_index", embeddings,  allow_dangerous_deserialization=True)
    return coarse_vdb, fine_vdb


def create_chain(model, *args, **kwargs):
    pass

from langchain_aws import ChatBedrock

class FixedChatBedrock(ChatBedrock):
    def _build_url(self) -> str:
        # Avoid re-encoding the model_id colon (`:` → %3A), which causes signature mismatch
        return f"/model/{self.model_id}/invoke"

def call_claude_3(prompt: str) -> str:
    load_dotenv()
    bedrock = boto3.client(
        service_name="bedrock-runtime",
        region_name=os.getenv("AWS_REGION", "us-west-2"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    )
    
    body = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 4000,
        "temperature": 0.0,
        "anthropic_version": "bedrock-2023-05-31"
    }

    response = bedrock.invoke_model(
        modelId="anthropic.claude-3-sonnet-20240229-v1:0",
        body=json.dumps(body),
        contentType="application/json",
        accept="application/json"
    )

    raw_body = response['body'].read()

    print("🪵 RAW:",raw_body)

    if not raw_body.strip():
        raise ValueError("Claude response body is empty.")

    try:
        result = json.loads(raw_body)
        embedded_text = result["content"][0]["text"]
        print("📝 Embedded text:", embedded_text)
        # # 2. Extract the array portion using regex (most reliable)
        # match = re.search(r"\[\s*\{.*?\}\s*\]", embedded_text, re.DOTALL)
        # if not match:
        #     raise ValueError("No JSON array found in content['text']")

        # json_array_string = match.group(0)

        # print("📜 JSON Array String:", json_array_string)

        # # 3. Parse the JSON array
        # actual_data = json.loads(json_array_string)
        # print('result:', actual_data)
        return extract_json_from_text(embedded_text)
    except json.JSONDecodeError as e:
        print("💥 Failed to decode JSON:", raw_body)
        raise e

def extract_json_from_text(text: str) -> str:
    start = text.find('{')
    if start == -1:
        raise ValueError("No JSON object found in text.")

    stack = []
    for i in range(start, len(text)):
        if text[i] == '{':
            stack.append('{')
        elif text[i] == '}':
            stack.pop()
            if not stack:
                return text[start:i+1]

    raise ValueError("No complete JSON object found.")

