import time
import logging
from app.services.vector_service import load_vector_db_for_company
from app.services.rag_service import retrieve_from_hierarchical_db

logger = logging.getLogger(__name__)

async def prefill_context_report(company_id: str, data: any):
    coarse_vdb, fine_vdb = load_vector_db_for_company(company_id)

    logger.info("Running hierarchical RAG for report generation…")
    start_time = time.time()
    overview = await retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data)
    end_time = time.time()

    logger.info("Duration: %.2fs", end_time - start_time)
    return overview

