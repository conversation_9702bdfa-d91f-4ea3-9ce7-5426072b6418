import os
import json
import fitz  # PyMuPDF
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from typing import List, Optional

# Import the AI service helper
from app.services.ai_service import process_context_report

async def prefillContextReport(company_id: str, data: any):
    """Main entry point for context report generation."""
    return await process_context_report(company_id, data)

# Utility functions that might be used elsewhere in the service
def split_docs(docs, chunk_size: int = 800, overlap: int = 50):
    """Split documents into chunks."""
    splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=overlap)
    return splitter.split_documents(docs)

def create_vector_db(chunks):
    """Create vector database from document chunks."""
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")
    return FAISS.from_documents(chunks, embeddings)

def json_to_text(data):
    """Convert JSON data to text format."""
    lines = []
    for item in data:
        lines.append(f"{item['id']}")
        lines.append(f"  {item['content']}\n")
    return "\n".join(lines)

