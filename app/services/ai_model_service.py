import os
import json
import boto3
import re
from dotenv import load_dotenv
from langchain_aws import ChatBedrock


class FixedChatBedrock(ChatBedrock):
    """Fixed version of ChatBedrock to avoid URL encoding issues."""
    def _build_url(self) -> str:
        # Avoid re-encoding the model_id colon (`:` → %3A), which causes signature mismatch
        return f"/model/{self.model_id}/invoke"


def call_claude_3(prompt: str) -> str:
    """Call Claude 3 via AWS Bedrock."""
    load_dotenv()
    bedrock = boto3.client(
        service_name="bedrock-runtime",
        region_name=os.getenv("AWS_REGION", "us-west-2"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    )
    
    body = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 4000,
        "temperature": 0.0,
        "anthropic_version": "bedrock-2023-05-31"
    }

    response = bedrock.invoke_model(
        modelId="anthropic.claude-3-sonnet-20240229-v1:0",
        body=json.dumps(body),
        contentType="application/json",
        accept="application/json"
    )

    raw_body = response['body'].read()
    print("🪵 RAW:", raw_body)

    if not raw_body.strip():
        raise ValueError("Claude response body is empty.")

    try:
        result = json.loads(raw_body)
        embedded_text = result["content"][0]["text"]
        print("📝 Embedded text:", embedded_text)
        return extract_json_from_text(embedded_text)
    except json.JSONDecodeError as e:
        print("💥 Failed to decode JSON:", raw_body)
        raise e


def extract_json_from_text(text: str) -> str:
    """Extract JSON object from text response."""
    start = text.find('{')
    if start == -1:
        raise ValueError("No JSON object found in text.")

    stack = []
    for i in range(start, len(text)):
        if text[i] == '{':
            stack.append('{')
        elif text[i] == '}':
            stack.pop()
            if not stack:
                return text[start:i+1]

    raise ValueError("No complete JSON object found.")
