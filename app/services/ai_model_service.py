import os
import json
import logging
import boto3
import re
from dotenv import load_dotenv
from langchain_aws import ChatBedrock

logger = logging.getLogger(__name__)


class FixedChatBedrock(ChatBedrock):
    def _build_url(self) -> str:
        # Avoid re-encoding the model_id colon (`:` → %3A), which causes signature mismatch
        return f"/model/{self.model_id}/invoke"


def handle_request_model_ai(prompt: str) -> str:
    """Handle AI model request with comprehensive error handling."""
    try:
        load_dotenv()

        # Validate input
        if not prompt or not prompt.strip():
            raise ValueError("Prompt cannot be empty")

        # Initialize AWS Bedrock client
        try:
            bedrock = boto3.client(
                service_name="bedrock-runtime",
                region_name=os.getenv("AWS_REGION", "us-west-2"),
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            )
        except Exception as e:
            logger.error("Failed to initialize AWS Bedrock client: %s", e)
            raise RuntimeError(f"AWS Bedrock client initialization failed: {e}")

        # Prepare request body
        body = {
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 4000,
            "temperature": 0.0,
            "anthropic_version": "bedrock-2023-05-31"
        }

        # Make API request
        try:
            response = bedrock.invoke_model(
                modelId="anthropic.claude-3-sonnet-20240229-v1:0",
                body=json.dumps(body),
                contentType="application/json",
                accept="application/json"
            )
        except Exception as e:
            # Log the full Bedrock error details
            logger.error("Bedrock API error: %s", str(e))

            # Check for specific Bedrock error types
            error_message = str(e)
            if "ThrottlingException" in error_message:
                logger.warning("Bedrock throttling detected - too many requests")
                raise RuntimeError(f"Bedrock throttling: {e}")
            elif "ValidationException" in error_message:
                logger.error("Bedrock validation error - invalid request")
                raise ValueError(f"Bedrock validation error: {e}")
            elif "AccessDeniedException" in error_message:
                logger.error("Bedrock access denied - check permissions")
                raise RuntimeError(f"Bedrock access denied: {e}")
            elif "ModelNotReadyException" in error_message:
                logger.error("Bedrock model not ready")
                raise RuntimeError(f"Bedrock model not ready: {e}")
            else:
                # Generic Bedrock error
                raise RuntimeError(f"Bedrock error: {e}")

        # Process response
        try:
            raw_body = response['body'].read()
            logger.debug("RAW response: %s", raw_body)

            if not raw_body or not raw_body.strip():
                raise ValueError("Claude response body is empty")

            result = json.loads(raw_body)

            # Validate response structure
            if "content" not in result or not result["content"]:
                raise ValueError("Invalid response structure: missing content")

            if not result["content"][0] or "text" not in result["content"][0]:
                raise ValueError("Invalid response structure: missing text in content")

            embedded_text = result["content"][0]["text"]
            logger.debug("Embedded text: %s", embedded_text)

            return extract_json_from_text(embedded_text)

        except json.JSONDecodeError as e:
            logger.error("Failed to decode JSON response: %s", raw_body)
            raise ValueError(f"Invalid JSON response from Claude: {e}")
        except KeyError as e:
            logger.error("Missing expected key in response: %s", e)
            raise ValueError(f"Invalid response format: missing key {e}")
        except Exception as e:
            logger.error("Error processing response: %s", e)
            raise RuntimeError(f"Response processing failed: {e}")

    except ValueError as e:
        # Re-raise validation errors as-is
        logger.error("Validation error: %s", e)
        raise
    except RuntimeError as e:
        # Re-raise runtime errors as-is
        logger.error("Runtime error: %s", e)
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error("Unexpected error in handle_request_model_ai: %s", e)
        raise RuntimeError(f"Unexpected error occurred: {e}")


def extract_json_from_text(text: str) -> str:
    """Extract JSON object from text response."""
    start = text.find('{')
    if start == -1:
        raise ValueError("No JSON object found in text.")

    stack = []
    for i in range(start, len(text)):
        if text[i] == '{':
            stack.append('{')
        elif text[i] == '}':
            stack.pop()
            if not stack:
                return text[start:i+1]

    raise ValueError("No complete JSON object found.")
