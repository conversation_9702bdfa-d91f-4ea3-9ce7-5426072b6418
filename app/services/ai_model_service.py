import os
import json
import logging
import boto3
import re
from dotenv import load_dotenv
from langchain_aws import ChatBedrock

logger = logging.getLogger(__name__)


class FixedChatBedrock(ChatBedrock):
    def _build_url(self) -> str:
        # Avoid re-encoding the model_id colon (`:` → %3A), which causes signature mismatch
        return f"/model/{self.model_id}/invoke"


def handle_request_model_ai(prompt: str) -> str:
    load_dotenv()
    bedrock = boto3.client(
        service_name="bedrock-runtime",
        region_name=os.getenv("AWS_REGION", "us-west-2"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    )
    
    body = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 4000,
        "temperature": 0.0,
        "anthropic_version": "bedrock-2023-05-31"
    }

    response = bedrock.invoke_model(
        modelId="anthropic.claude-3-sonnet-20240229-v1:0",
        body=json.dumps(body),
        contentType="application/json",
        accept="application/json"
    )

    raw_body = response['body'].read()
    logger.debug("RAW response: %s", raw_body)

    if not raw_body.strip():
        raise ValueError("Claude response body is empty.")

    try:
        result = json.loads(raw_body)
        embedded_text = result["content"][0]["text"]
        logger.debug("📝 Embedded text: %s", embedded_text)
        return extract_json_from_text(embedded_text)
    except json.JSONDecodeError as e:
        logger.error("Failed to decode JSON: %s", raw_body)
        raise e


def extract_json_from_text(text: str) -> str:
    """Extract JSON object from text response."""
    start = text.find('{')
    if start == -1:
        raise ValueError("No JSON object found in text.")

    stack = []
    for i in range(start, len(text)):
        if text[i] == '{':
            stack.append('{')
        elif text[i] == '}':
            stack.pop()
            if not stack:
                return text[start:i+1]

    raise ValueError("No complete JSON object found.")
