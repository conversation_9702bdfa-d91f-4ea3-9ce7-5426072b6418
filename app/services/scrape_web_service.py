import os
import requests
from bs4 import BeautifulSoup
from typing import List
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document


def scrape_structured_webpage(url: str) -> list[Document]:
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')

    # Try to focus on main content only
    content = soup.find('main') or soup.find('body')
    if content is None:
        content = soup

    lines = []

    def extract_text_recursively(elem):
        for child in elem.children:
            if child.name in ['h1', 'h2', 'h3']:
                lines.append(f"\n## {child.get_text(strip=True)}\n")
            elif child.name in ['p', 'li']:
                lines.append(child.get_text(strip=True))
            elif child.name in ['strong', 'b']:
                lines.append(f"**{child.get_text(strip=True)}**")
            elif child.name == 'a':
                text = child.get_text(strip=True)
                href = child.get('href', '')
                if href:
                    lines.append(f"[{text}]({href})")
                else:
                    lines.append(text)
            elif child.name in ['div', 'section', 'article', 'span']:
                extract_text_recursively(child)
            elif isinstance(child, str):
                text = child.strip()
                if text:
                    lines.append(text)

    extract_text_recursively(content)

    markdown_text = "\n".join(lines).strip()
    print("MARKD", markdown_text)

    return [Document(
        page_content=markdown_text,
        metadata={
            "source": url,
            "doc_source": url,
            "always_include": True
        }
    )]


def create_hierarchical_vector_db(docs):
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")
    
    coarse_docs = []
    fine_docs = []

    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)
    
    for doc in docs:
        # If it's a website doc, don't chunk — add as one document
        if doc.metadata.get("source", "").startswith("http"):
          chunks = splitter.split_documents([doc])
          for i, chunk in enumerate(chunks):
              chunk.metadata.update({
                  "doc_source": doc.metadata["source"],
                  "always_include": True,
                  "chunk_id": i,
                  "level": "fine"
              })
              fine_docs.append(chunk)

          # Optionally keep one for coarse index:
          rep_chunk = chunks[0]
          rep_chunk.metadata.update({
            "doc_source": doc.metadata["source"],
            "always_include": True,
            "level": "coarse"
          })
          coarse_docs.append(rep_chunk)
        else:
            # For PDFs or other docs, do chunking as before
            chunks = splitter.split_documents([doc])

            rep_chunk = chunks[0]
            rep_chunk.metadata["doc_source"] = doc.metadata["source"]
            rep_chunk.metadata["always_include"] = doc.metadata["always_include"]
            rep_chunk.metadata["level"] = "coarse"
            coarse_docs.append(rep_chunk)

            for i, chunk in enumerate(chunks):
                chunk.metadata["doc_source"] = doc.metadata["source"]
                chunk.metadata["always_include"] = doc.metadata["always_include"]
                chunk.metadata["chunk_id"] = i
                chunk.metadata["level"] = "fine"
                fine_docs.append(chunk)

    coarse_vdb = FAISS.from_documents(coarse_docs, embeddings)
    fine_vdb = FAISS.from_documents(fine_docs, embeddings)
    return coarse_vdb, fine_vdb


def scrape_multiple_webpages(company_id: str, urls: List[str]):
    all_documents = []
    print(f"🌐 Scraping {len(urls)} webpages for company {company_id} {urls}")
    for url in urls:
        try:
            documents = scrape_structured_webpage(url)
            all_documents.extend(documents)
            if len(all_documents) > 0:
                coarse_vdb, fine_vdb = create_hierarchical_vector_db(all_documents)

                base_path = f"./vector_dbs/{company_id}"
                os.makedirs(base_path, exist_ok=True)

                coarse_vdb.save_local(f"{base_path}/coarse_index")
                fine_vdb.save_local(f"{base_path}/fine_index")
        except Exception as e:
            print(f"Failed to scrape {url}: {e}")
