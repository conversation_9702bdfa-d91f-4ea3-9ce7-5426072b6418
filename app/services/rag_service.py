import logging
from app.services.prompt import get_context_report_data, extract_prompt_list
from app.services.embedding_service import count_tokens
from app.services.ai_model_service import handle_request_model_ai

logger = logging.getLogger(__name__)


async def retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data: any, k_docs: int = 3):
    logger.info("Running hierarchical RAG for report generation…")
    queries = extract_prompt_list(data)
    prompt = get_context_report_data(data)

    # Step 1: Coarse retrieval to identify relevant documents
    top_docs = set()
    for q in queries:
        coarse_hits = coarse_vdb.similarity_search(q, k=k_docs)
        top_docs.update(hit.metadata["doc_source"] for hit in coarse_hits)

    # Include documents marked as always_include
    for doc in coarse_vdb.docstore._dict.values():
        if doc.metadata.get("always_include", False):
            top_docs.add(doc.metadata["doc_source"])

    logger.info("Top doc sources selected: %s", top_docs)

    # Step 2: Fine retrieval from selected documents
    all_chunks = []
    for q in queries:
        fine_hits = fine_vdb.similarity_search_with_score(q, k=20)
        for doc, score in fine_hits:
            if doc.metadata["doc_source"] in top_docs:
                all_chunks.append(doc)

    # Deduplicate chunks
    seen = set()
    unique_chunks = []
    for doc in all_chunks:
        if doc.page_content not in seen:
            unique_chunks.append(doc)
            seen.add(doc.page_content)

    # Build context and generate response
    context = "\n\n".join(
        f"[Source: {doc.metadata['doc_source']}]\n{doc.page_content}"
        for doc in unique_chunks)
    total_prompt = prompt + "\n\nContext:\n" + context
    logger.info("🧾 Input tokens: %d", count_tokens(total_prompt))

    response_text = handle_request_model_ai(total_prompt)

    # Count output tokens
    output_tokens = count_tokens(response_text)
    logger.info("Output tokens: %d", output_tokens)

    return response_text
