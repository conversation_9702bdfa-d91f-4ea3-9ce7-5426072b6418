import logging

logger = logging.getLogger(__name__)

def get_context_report_data(data):
    outline_lines = [f'{p.id}: {p.prompt}' for p in data.prompts]
    outline = "\n".join(outline_lines)

    full_prompt = f"""You are a senior risk analyst preparing the 'Context Report' from which will be generated a Risk Assessment Report.  
        This is a report describing key concepts for a business leader to review, edit, and ensure the context of the engagement is correct.

        Using the context provided, complete the following structured outline. Write in a professional, simple, and easy-to-understand style. Prioritize clarity and conciseness.  
        Avoid jargon unless necessary, and explain any technical terms when used. The tone should be confident, informative, and approachable—suitable for a business audience across different levels of expertise.

        Each item in the array should be a JSON object with these fields:  
        - `id`: a unique identifier (e.g., "4910e8ef-eb42-4b61-9afb-e1c73c565aa8")  
        - `content`: a clear and concise business-style explanation (use the word "company" instead of the full name)  
        - `citedFrom`: name of the source document, e.g., "Annual Report"

        If a question cannot be answered, leave it out entirely. DO NOT include "not found" or "no data available".

        Input Format:  
        1. an array of prompts, each with an `id` and `prompt` field

        Output format:  
        1. Your response should be **only** the valid JSON object, with no additional text, explanations, or formatting.  
        2. Do **not** include any phrases like "Here is...", "The response is:", or any other preamble or commentary.  
        3. Return only the JSON  
        4. Id is the same as the id in input data  
        5. Content is the response to the prompt  
        6. CitedFrom is the name of the source document from which the content was derived. Use the metadata field 'doc_source' from the source chunk or document to populate this field exactly as stored (e.g., "2023_Annual_Report.pdf")

        Example input:  
        {{  
          "prompts": [  
            {{  
              "id": "8",  
              "prompt": "Employee Demographics: What is the breakdown of FTE vs part-time vs contractors, etc.?"  
            }}
          ]  
        }}

        Example output:  
        {{  
          "id": "8",  
          "content": "As of December 31, 2024, the company had 939 employees and 66 consultants and contractors. None of the U.S. based employees were represented by labor unions or covered by collective bargaining agreements, although certain international employees were covered by collective labor agreements established under local law. The company considers its relationship with employees to be good.",  
          "citedFrom": "Annual Report"  
        }}

        == OUTLINE TO COMPLETE ==  
        {outline}

        ---

        IMPORTANT:  
        - Respond **only** with a valid JSON object matching the specified format.  
        - Do NOT include any introductory text, explanations, or comments.  
        - Do NOT add any extra whitespace, line breaks outside of JSON string escapes, or formatting outside JSON syntax.  
        - Return exactly one JSON object — nothing else.  
        - Strings must be properly escaped to produce valid JSON.  
        - Do NOT wrap the JSON in markdown code blocks or any other delimiters.
  """

    logger.debug("Full prompt: %s", full_prompt)
    return full_prompt

def extract_prompt_list(prompts):
    return [f'{p.id}: {p.prompt}' for p in prompts.prompts]





