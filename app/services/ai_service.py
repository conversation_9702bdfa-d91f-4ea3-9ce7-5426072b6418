import os
import json
import time
import tiktoken
import boto3
import re
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_aws import ChatBedrock
from app.services.prompt import get_context_report_data, extract_prompt_list


def count_tokens(text: str, model_name: str = "claude-sonnet-4-20250514") -> int:
    """Count tokens in text for the given model."""
    if "gpt" in model_name:
        enc = tiktoken.encoding_for_model(model_name)
        return len(enc.encode(text))
    else:
        return len(text) // 4


def load_vector_db_for_company(company_id: str):
    """Load vector databases for a specific company."""
    base_path = f"./vector_dbs/{company_id}"
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/paraphrase-MiniLM-L6-v2")

    coarse_vdb = FAISS.load_local(
        f"{base_path}/coarse_index",
        embeddings,
        allow_dangerous_deserialization=True
    )
    fine_vdb = FAISS.load_local(f"{base_path}/fine_index", embeddings, allow_dangerous_deserialization=True)
    return coarse_vdb, fine_vdb


async def retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data: any, k_docs: int = 3):
    """Retrieve relevant documents and generate response using hierarchical RAG."""
    queries = extract_prompt_list(data)
    prompt = get_context_report_data(data)

    # Step 1: Coarse retrieval to identify relevant documents
    top_docs = set()
    for q in queries:
        coarse_hits = coarse_vdb.similarity_search(q, k=k_docs)
        top_docs.update(hit.metadata["doc_source"] for hit in coarse_hits)

    # Include documents marked as always_include
    for doc in coarse_vdb.docstore._dict.values():
        if doc.metadata.get("always_include", False):
            top_docs.add(doc.metadata["doc_source"])

    print(f"📘 Top doc sources selected: {top_docs}")

    # Step 2: Fine retrieval from selected documents
    all_chunks = []
    for q in queries:
        fine_hits = fine_vdb.similarity_search_with_score(q, k=20)
        for doc, score in fine_hits:
            if doc.metadata["doc_source"] in top_docs:
                all_chunks.append(doc)

    # Deduplicate chunks
    seen = set()
    unique_chunks = []
    for doc in all_chunks:
        if doc.page_content not in seen:
            unique_chunks.append(doc)
            seen.add(doc.page_content)

    # Build context and generate response
    context = "\n\n".join(
        f"[Source: {doc.metadata['doc_source']}]\n{doc.page_content}"
        for doc in unique_chunks)
    total_prompt = prompt + "\n\nContext:\n" + context
    print(f"🧾 Input tokens: {count_tokens(total_prompt)}")
    
    response_text = call_claude_3(total_prompt)
    return response_text


class FixedChatBedrock(ChatBedrock):
    """Fixed version of ChatBedrock to avoid URL encoding issues."""
    def _build_url(self) -> str:
        # Avoid re-encoding the model_id colon (`:` → %3A), which causes signature mismatch
        return f"/model/{self.model_id}/invoke"


def call_claude_3(prompt: str) -> str:
    """Call Claude 3 via AWS Bedrock."""
    load_dotenv()
    bedrock = boto3.client(
        service_name="bedrock-runtime",
        region_name=os.getenv("AWS_REGION", "us-west-2"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    )
    
    body = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 4000,
        "temperature": 0.0,
        "anthropic_version": "bedrock-2023-05-31"
    }

    response = bedrock.invoke_model(
        modelId="anthropic.claude-3-sonnet-20240229-v1:0",
        body=json.dumps(body),
        contentType="application/json",
        accept="application/json"
    )

    raw_body = response['body'].read()
    print("🪵 RAW:", raw_body)

    if not raw_body.strip():
        raise ValueError("Claude response body is empty.")

    try:
        result = json.loads(raw_body)
        embedded_text = result["content"][0]["text"]
        print("📝 Embedded text:", embedded_text)
        return extract_json_from_text(embedded_text)
    except json.JSONDecodeError as e:
        print("💥 Failed to decode JSON:", raw_body)
        raise e


def extract_json_from_text(text: str) -> str:
    """Extract JSON object from text response."""
    start = text.find('{')
    if start == -1:
        raise ValueError("No JSON object found in text.")

    stack = []
    for i in range(start, len(text)):
        if text[i] == '{':
            stack.append('{')
        elif text[i] == '}':
            stack.pop()
            if not stack:
                return text[start:i+1]

    raise ValueError("No complete JSON object found.")


async def process_context_report(company_id: str, data: any):
    """Main function to process context report generation."""
    print(f"📦 Loading vector DBs for company {company_id}…")
    coarse_vdb, fine_vdb = load_vector_db_for_company(company_id)

    print("🤖 Running hierarchical RAG for report generation…")
    start_time = time.time()
    overview = await retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data)
    end_time = time.time()

    print(f"\n⏱️ Duration: {end_time - start_time:.2f}s")
    return overview
