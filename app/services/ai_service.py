import time
from app.services.vector_service import load_vector_db_for_company
from app.services.rag_service import retrieve_from_hierarchical_db





async def process_context_report(company_id: str, data: any):
    """Main function to process context report generation."""
    print(f"📦 Loading vector DBs for company {company_id}…")
    coarse_vdb, fine_vdb = load_vector_db_for_company(company_id)

    print("🤖 Running hierarchical RAG for report generation…")
    start_time = time.time()
    overview = await retrieve_from_hierarchical_db(coarse_vdb, fine_vdb, data)
    end_time = time.time()

    print(f"\n⏱️ Duration: {end_time - start_time:.2f}s")
    return overview
