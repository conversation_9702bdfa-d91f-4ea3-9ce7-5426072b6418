# app/main.py

import logging
from fastapi import FastAPI
from app.controllers.ai_controller import router as ai_assistant_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
    ]
)

app = FastAPI(title="AI Service")

# Include router
app.include_router(ai_assistant_router, prefix="/ai")
