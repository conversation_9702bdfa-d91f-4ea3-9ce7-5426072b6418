

import os
import json
import logging
from dotenv import load_dotenv
from app.models.input_output import InputData, OutputData, ScrapeRequest
from app.services.context_report_service import prefill_context_report
from app.models.input_output import OutputData
from fastapi import APIRouter, HTTPException, Query
from app.services.ingest_docs_service import ingest_documents_for_company
from app.services.scrape_web_service import scrape_multiple_webpages
from uuid import UUID


router = APIRouter()
logger = logging.getLogger(__name__)

load_dotenv()

@router.post("/context-report", response_model=OutputData)
async def prefill_context_report_section(
    data: InputData,
    company_id: UUID = Query(..., description="Company UUID (v4)")
):  
    response = await prefill_context_report(company_id, data)
    return json.loads(response)
    
@router.post("/ingest-docs")
async def ingest_docs(
    company_id: UUID = Query(..., description="Company UUID (v4)")
):
    try:
        ingest_documents_for_company(
            company_id=company_id,
            bucket=os.getenv("AWS_S3_BUCKET"),
            s3_prefix=f"documents/{company_id}/"
        )
        return {"message": "Ingestion complete"}

    except Exception as e:
        logger.exception("Failed to ingest documents for company %s", company_id)
        raise HTTPException(status_code=500, detail=str(e))
    
#We won't use this endpoint for now
@router.post("/scrape-web")
def scrape_webpages(request: ScrapeRequest, company_id: UUID = Query(..., description="Company UUID (v4)")):
    url_strings = [str(url) for url in request.urls]
    try:
        scrape_multiple_webpages(str(company_id), url_strings)
        return {
            "status": "success",
            "company_id": str(company_id),
            "scraped_urls": url_strings
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
