
from app.models.input_output import InputData, OutputData, ScrapeRequest
from app.services.context_report_service import prefill_context_report
from app.models.input_output import OutputData
from fastapi import APIRouter, UploadFile, File, HTTPException, Query
from app.services.ingest_docs_service import ingest_documents_for_company
from app.services.scrape_web_service import scrape_multiple_webpages
from uuid import UUID
from typing import List
import os
import shutil
import tempfile
import json
import boto3
from dotenv import load_dotenv
import boto3
from io import BytesIO
import fitz
from langchain.document_loaders import PyMuPDFLoader


router = APIRouter()

@router.post("/context-report", response_model=OutputData)
async def prefill_context_report_section(
    data: InputData,
    company_id: UUID = Query(..., description="Company UUID (v4)")
):  
    load_dotenv()
    s3 = boto3.client('s3')
    paginator = s3.get_paginator("list_objects_v2")
    keys = []

    print("paginator", paginator)
    for page in paginator.paginate(Bucket='eastward-dev-files', Prefix='documents/0c73ece7-c908-49b1-8dc7-463a44297510/'):
        for obj in page.get("Contents", []):
            keys.append(obj["Key"])
    print('Keys:', keys)

    docs = load_pdfs_from_s3(bucket='eastward-dev-files', keys=keys)

    ingest_documents_for_company(company_id, docs)
    # response = await prefill_context_report(company_id, data)
    return json.loads([])

@router.post("/ingest-docs")
async def ingest_docs(
    company_id: UUID = Query(..., description="Company UUID (v4)"),
    files: list[UploadFile] = File(...)
):
  with tempfile.TemporaryDirectory() as tmpdir:
      for file in files:
          if not file.filename.lower().endswith(".pdf"):
              raise HTTPException(status_code=400, detail="Only PDF files allowed")

          dest_path = os.path.join(tmpdir, file.filename)
          with open(dest_path, "wb") as f:
              shutil.copyfileobj(file.file, f)
      ingest_documents_for_company(str(company_id), tmpdir)

  return {
      "status": "success",
      "company_id": str(company_id),
      "uploaded_files": [f.filename for f in files]
  }

#We won't use this endpoint for now
@router.post("/scrape-web")
def scrape_webpages(request: ScrapeRequest, company_id: UUID = Query(..., description="Company UUID (v4)")):
    url_strings = [str(url) for url in request.urls]
    try:
        scrape_multiple_webpages(str(company_id), url_strings)
        return {
            "status": "success",
            "company_id": str(company_id),
            "scraped_urls": url_strings
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

def load_pdfs_from_s3(bucket: str, keys: list[str]) -> list:
    s3 = boto3.client("s3")
    docs = []
    for key in keys:
        obj = s3.get_object(Bucket=bucket, Key=key)
        buffer = BytesIO(obj['Body'].read())

        # Save to in-memory temp file if necessary for loader
        with open("/tmp/temp.pdf", "wb") as f:
            f.write(buffer.getvalue())

        loader = PyMuPDFLoader("/tmp/temp.pdf")
        docs.extend(loader.load())

    return docs