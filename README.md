# Eastward AI

This is a FastAPI-based AI service project containerized with Docker.

---

## Requirements

- [Docker Desktop](https://www.docker.com/products/docker-desktop) installed on your machine  
  (includes Docker Engine and CLI)

> No need to install Python, pip, or dependencies manually — everything runs inside Docker.

---

## How to Run

```bash
docker-compose up --build
```

## Notes
If you get credential errors building the Docker image, check your Docker config file (~/.docker/config.json) for problematic credential helpers (like "credsStore": "desktop") and remove/comment them.
